using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace DEAR_Dental_Clinic.Pages
{
    public class ContactModel : PageModel
    {
        private readonly ILogger<ContactModel> _logger;

        public ContactModel(ILogger<ContactModel> logger)
        {
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            [Required(ErrorMessage = "First name is required")]
            [Display(Name = "First Name")]
            public string FirstName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Last name is required")]
            [Display(Name = "Last Name")]
            public string LastName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Email is required")]
            [EmailAddress(ErrorMessage = "Please enter a valid email address")]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;

            [Phone(ErrorMessage = "Please enter a valid phone number")]
            [Display(Name = "Phone")]
            public string Phone { get; set; } = string.Empty;

            [Required(ErrorMessage = "Subject is required")]
            [Display(Name = "Subject")]
            public string Subject { get; set; } = string.Empty;

            [Required(ErrorMessage = "Message is required")]
            [Display(Name = "Message")]
            public string Message { get; set; } = string.Empty;
        }

        public void OnGet()
        {
            _logger.LogInformation("Contact page accessed at {Time}", DateTime.Now);
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // TODO: Implement email sending logic here
                _logger.LogInformation("Contact form submitted by {Email} at {Time}", Input.Email, DateTime.Now);
                
                // For now, just log the message
                _logger.LogInformation("Contact message: {Subject} - {Message}", Input.Subject, Input.Message);
                
                // Add success message
                TempData["SuccessMessage"] = "Thank you for your message! We will get back to you soon.";
                
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing contact form submission");
                ModelState.AddModelError(string.Empty, "An error occurred while sending your message. Please try again.");
                return Page();
            }
        }
    }
}
