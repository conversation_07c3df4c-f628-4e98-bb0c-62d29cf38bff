@page
@model DEAR_Dental_Clinic.Pages.BookAppointmentModel
@{
    ViewData["Title"] = "Book Appointment - DEAR Dental Clinic";
}

<!-- MDB5 Book Appointment -->
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow-5 rounded-6" data-mdb-animation-init data-mdb-animation="fade-in-up">
                <div class="card-body p-5">
                    <div class="text-center mb-5">
                        <i class="fas fa-calendar-plus fa-4x text-primary mb-3"></i>
                        <h1 class="display-6 fw-bold text-primary">Book Your Appointment</h1>
                        <p class="lead text-muted">Schedule your visit with DEAR Dental Clinic</p>
                    </div>

                    <form method="post">
                        <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success alert-dismissible fade show shadow-3" role="alert">
                                <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-mdb-dismiss="alert"></button>
                            </div>
                        }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.FirstName" class="form-label">First Name</label>
                            <input asp-for="Input.FirstName" class="form-control" placeholder="Your first name" />
                            <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.LastName" class="form-label">Last Name</label>
                            <input asp-for="Input.LastName" class="form-control" placeholder="Your last name" />
                            <span asp-validation-for="Input.LastName" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" placeholder="<EMAIL>" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.Phone" class="form-label">Phone</label>
                            <input asp-for="Input.Phone" class="form-control" placeholder="(*************" />
                            <span asp-validation-for="Input.Phone" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.PreferredDate" class="form-label">Preferred Date</label>
                            <input asp-for="Input.PreferredDate" type="date" class="form-control" />
                            <span asp-validation-for="Input.PreferredDate" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.PreferredTime" class="form-label">Preferred Time</label>
                            <select asp-for="Input.PreferredTime" class="form-control">
                                <option value="">Select a time</option>
                                <option value="8:00 AM">8:00 AM</option>
                                <option value="9:00 AM">9:00 AM</option>
                                <option value="10:00 AM">10:00 AM</option>
                                <option value="11:00 AM">11:00 AM</option>
                                <option value="1:00 PM">1:00 PM</option>
                                <option value="2:00 PM">2:00 PM</option>
                                <option value="3:00 PM">3:00 PM</option>
                                <option value="4:00 PM">4:00 PM</option>
                            </select>
                            <span asp-validation-for="Input.PreferredTime" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Input.ServiceType" class="form-label">Type of Service</label>
                        <select asp-for="Input.ServiceType" class="form-control">
                            <option value="">Select a service</option>
                            <option value="General Checkup">General Checkup</option>
                            <option value="Dental Cleaning">Dental Cleaning</option>
                            <option value="Cosmetic Dentistry">Cosmetic Dentistry</option>
                            <option value="Oral Surgery">Oral Surgery</option>
                            <option value="Preventive Care">Preventive Care</option>
                            <option value="Restorative Dentistry">Restorative Dentistry</option>
                            <option value="Emergency">Emergency</option>
                            <option value="Other">Other</option>
                        </select>
                        <span asp-validation-for="Input.ServiceType" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Input.Notes" class="form-label">Additional Notes</label>
                        <textarea asp-for="Input.Notes" class="form-control" rows="4" placeholder="Any specific concerns or requests..."></textarea>
                        <span asp-validation-for="Input.Notes" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="Input.IsNewPatient" class="form-check-input" type="checkbox" />
                            <label asp-for="Input.IsNewPatient" class="form-check-label">
                                I am a new patient
                            </label>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-calendar-check me-2"></i>Book Appointment
                        </button>
                    </div>

                    <div class="text-center mt-3">
                        <p class="text-muted">
                            <small>We will contact you within 24 hours to confirm your appointment.</small>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
