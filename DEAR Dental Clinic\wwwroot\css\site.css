/* Base Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

/* Focus Styles */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Brand Colors */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

/* Landing Page Styles */
.landing-page {
  overflow-x: hidden;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.brand-highlight {
  color: #ffd700;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons .btn {
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 50px;
}

.hero-image {
  text-align: center;
}

.image-placeholder {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
}

/* Services Section */
.services-section {
  background: #f8f9fa;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.service-card h4 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

/* About Section */
.about-section {
  padding: 5rem 0;
}

.about-features {
  list-style: none;
  padding: 0;
}

.about-features li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
}

.about-features i {
  margin-right: 0.5rem;
}

/* Contact Section */
.contact-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
}

.contact-card:hover {
  transform: translateY(-3px);
}

.contact-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Login Page Styles */
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-title {
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #666;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.input-group-text {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
}

.form-control {
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  border-radius: 8px;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.login-btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 8px;
}

.forgot-password-link {
  color: var(--primary-color);
  text-decoration: none;
}

.forgot-password-link:hover {
  text-decoration: underline;
}

.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #dee2e6;
}

.divider span {
  background: white;
  padding: 0 1rem;
  color: #666;
}

.staff-access .card {
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* Responsive Design - Media Queries */

/* Extra Small Devices (max-width: 575px) */
@media (max-width: 575px) {
  html {
    font-size: 12px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .service-card, .contact-card {
    margin-bottom: 1.5rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  .hero-section {
    padding: 2rem 0;
  }

  .image-placeholder {
    padding: 2rem;
  }
}

/* Small Devices (min-width: 576px and max-width: 767px) */
@media (min-width: 576px) and (max-width: 767px) {
  html {
    font-size: 13px;
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-buttons .btn {
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .service-card {
    margin-bottom: 2rem;
  }

  .login-card {
    padding: 2rem;
  }

  .navbar-nav {
    text-align: center;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 3rem;
  }
}

/* Medium Devices (min-width: 768px and max-width: 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  html {
    font-size: 14px;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.15rem;
  }

  .section-title {
    font-size: 2.3rem;
  }

  .service-card {
    margin-bottom: 2rem;
  }

  .contact-card {
    margin-bottom: 2rem;
  }

  .login-card {
    padding: 2.5rem;
  }

  .hero-content {
    margin-bottom: 2rem;
  }

  .about-content {
    margin-top: 2rem;
  }
}

/* Large Devices (min-width: 992px and max-width: 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  html {
    font-size: 15px;
  }

  .hero-title {
    font-size: 3.2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2.4rem;
  }

  .service-card {
    margin-bottom: 2rem;
  }

  .contact-card {
    margin-bottom: 2rem;
  }

  .hero-buttons .btn {
    margin-right: 1rem;
  }
}

/* Extra Large Devices (min-width: 1200px) */
@media (min-width: 1200px) {
  html {
    font-size: 16px;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .container {
    max-width: 1200px;
  }
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Print Styles */
@media print {
  .navbar, .footer, .hero-buttons, .staff-access {
    display: none !important;
  }

  .hero-section {
    background: none !important;
    color: black !important;
  }

  .service-card, .contact-card, .login-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}