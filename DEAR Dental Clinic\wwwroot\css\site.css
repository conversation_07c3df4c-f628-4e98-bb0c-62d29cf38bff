/* Base Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.brand-highlight {
  color: #ffd700;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.hero-buttons .btn {
  padding: 12px 30px;
  margin-bottom: 1rem;
}

.hero-image {
  text-align: center;
}

.image-placeholder {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 3rem;
}

/* Services Section */
.services-section {
  background: #f8f9fa;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  margin-bottom: 2rem;
}

.service-card:hover {
  transform: translateY(-3px);
}

.service-icon {
  font-size: 3rem;
  color: #007bff;
  margin-bottom: 1.5rem;
}

.service-card h4 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

/* About Section */
.about-features {
  list-style: none;
  padding: 0;
}

.about-features li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
}

.about-features i {
  margin-right: 0.5rem;
}

/* Contact Section */
.contact-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  margin-bottom: 2rem;
}

.contact-icon {
  font-size: 2.5rem;
  color: #007bff;
  margin-bottom: 1rem;
}

/* Login Page */
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Additional Page Styles */
.service-list {
  list-style: none;
  padding: 0;
}

.service-list li {
  padding: 0.3rem 0;
  color: #666;
}

.service-list li:before {
  content: "• ";
  color: #007bff;
  font-weight: bold;
  margin-right: 0.5rem;
}

.team-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  margin-bottom: 2rem;
}

.team-image {
  margin-bottom: 1rem;
}

.team-role {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-form-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.office-hours-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
}

.time {
  color: #666;
}

/* Appointment Booking */
.appointment-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Navigation Responsive */
.navbar-collapse {
  align-items: center;
}

.navbar-nav {
  margin: 0 auto;
}

/* Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* Responsive Design - Media Queries */

/* Extra Small Devices (max-width: 575px) */
@media (max-width: 575px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .image-placeholder {
    padding: 2rem;
  }

  /* Navigation responsive for mobile */
  .navbar-collapse {
    flex-direction: column !important;
  }

  .navbar-nav {
    text-align: center;
    margin: 1rem 0;
  }

  .navbar-collapse > div:last-child {
    margin-top: 1rem;
  }
}

/* Small Devices (min-width: 576px and max-width: 767px) */
@media (min-width: 576px) and (max-width: 767px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  /* Navigation responsive for small tablets */
  .navbar-collapse {
    flex-direction: column !important;
  }

  .navbar-nav {
    text-align: center;
    margin: 1rem 0;
  }
}

/* Medium Devices (min-width: 768px and max-width: 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  html {
    font-size: 15px;
  }
}

/* Large Devices (min-width: 992px and max-width: 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  html {
    font-size: 16px;
  }
}