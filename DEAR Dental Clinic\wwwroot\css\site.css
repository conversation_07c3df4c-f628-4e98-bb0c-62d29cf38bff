/* MDB5 Custom Styles */
html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}

/* MDB5 Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Custom hover effects for MDB5 cards */
.hover-shadow {
  transition: all 0.3s ease;
}

.hover-shadow:hover {
  transform: translateY(-5px);
}

/* MDB5 Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  animation: fadeInLeft 1s ease-out;
}

.hero-image {
  text-center;
  animation: fadeInRight 1s ease-out;
}

.hero-buttons .btn {
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
  transform: translateY(-2px);
}

/* MDB5 Animation keyframes */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* MDB5 Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* MDB5 Custom Card Styles */
.card {
  border: none;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

/* MDB5 Form Enhancements */
.form-outline {
  position: relative;
}

.form-outline .form-control {
  border-radius: 0.5rem;
}

/* MDB5 Page-specific Styles */
.login-page {
  min-height: 100vh;
}

.about-features {
  list-style: none;
  padding: 0;
}

.about-features li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
}

.about-features i {
  margin-right: 0.5rem;
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  font-weight: 600;
}

.time {
  color: #666;
}

/* MDB5 Navigation Responsive */
.navbar-collapse {
  align-items: center;
}

.navbar-nav {
  margin: 0 auto;
}

/* Utility Classes */
.min-vh-100 {
  min-height: 100vh;
}

/* Responsive Design - Media Queries */

/* Extra Small Devices (max-width: 575px) */
@media (max-width: 575px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .login-card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .image-placeholder {
    padding: 2rem;
  }

  /* Navigation responsive for mobile */
  .navbar-collapse {
    flex-direction: column !important;
  }

  .navbar-nav {
    text-align: center;
    margin: 1rem 0;
  }

  .navbar-collapse > div:last-child {
    margin-top: 1rem;
  }
}

/* Small Devices (min-width: 576px and max-width: 767px) */
@media (min-width: 576px) and (max-width: 767px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  /* Navigation responsive for small tablets */
  .navbar-collapse {
    flex-direction: column !important;
  }

  .navbar-nav {
    text-align: center;
    margin: 1rem 0;
  }
}

/* Medium Devices (min-width: 768px and max-width: 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  html {
    font-size: 15px;
  }
}

/* Large Devices (min-width: 992px and max-width: 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  html {
    font-size: 16px;
  }
}