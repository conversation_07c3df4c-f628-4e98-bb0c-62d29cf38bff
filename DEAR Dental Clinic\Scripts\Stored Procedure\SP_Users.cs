using Microsoft.Data.SqlClient;
using DEAR_Dental_Clinic.Scripts.Helper;

namespace DEAR_Dental_Clinic.Scripts.Stored_Procedure
{
    public class SP_Users
    {
        private readonly DatabaseHelper _dbHelper;

        public SP_Users()
        {
            _dbHelper = new DatabaseHelper();
        }

        /// <summary>
        /// Creates the Users table in the database if it doesn't already exist
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool CreateUsersTable()
        {
            string createTableQuery = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                BEGIN
                    CREATE TABLE Users (
                        Id INT PRIMARY KEY IDENTITY(1,1),
                        Username NVARCHAR(255) UNIQUE NOT NULL,
                        Email NVARCHAR(255) UNIQUE NOT NULL,
                        Password NVARCHAR(MAX) NOT NULL,
                        Role NVARCHAR(50) NOT NULL CHECK (Role IN ('Admin', 'User')),
                        FirstName NVARCHAR(MAX) NULL,
                        LastName NVARCHAR(MAX) NULL,
                        ProfileImage VARBINARY(MAX) NULL,
                        DateOfBirth DATE NULL,
                        LastLoginAt DATETIME2 NULL,
                        CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
                        UpdatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
                    );

                    PRINT 'Users table created successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Users table already exists.';
                END";

            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(createTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Users table creation process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error creating Users table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error creating Users table: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Drops the Users table if it exists (use with caution)
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        public bool DropUsersTable()
        {
            string dropTableQuery = @"
                IF EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                BEGIN
                    DROP TABLE Users;
                    PRINT 'Users table dropped successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Users table does not exist.';
                END";

            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(dropTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Users table drop process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error dropping Users table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error dropping Users table: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if the Users table exists in the database
        /// </summary>
        /// <returns>True if table exists, false otherwise</returns>
        public bool CheckUsersTableExists()
        {
            string checkTableQuery = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'Users' AND TABLE_SCHEMA = 'dbo'";

            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
                    {
                        int count = (int)command.ExecuteScalar();
                        bool exists = count > 0;
                        Console.WriteLine($"Users table exists: {exists}");
                        return exists;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error checking Users table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error checking Users table: {ex.Message}");
                return false;
            }
        }
    }
}