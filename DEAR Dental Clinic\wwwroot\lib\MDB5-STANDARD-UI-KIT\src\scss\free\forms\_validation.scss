//
// Material styles for form validation
//

@mixin form-validation-state-selector($state) {
  @if ($state == 'valid' or $state == 'invalid') {
    .was-validated #{if(&, "&", "")}:#{$state},
    #{if(&, "&", "")}.is-#{$state} {
      @content;
    }
  } @else {
    #{if(&, "&", "")}.is-#{$state} {
      @content;
    }
  }
}

@mixin form-validation-state-mdb(
  $state,
  $color,
  $tooltip-color: color-contrast($color),
  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),
  $focus-box-shadow: 0 0 0 $input-focus-width rgba($color, $input-btn-focus-color-opacity)
) {
  .#{$state}-feedback {
    position: absolute;
    display: none;
    width: auto;
    margin-top: $form-feedback-margin-top;
    font-size: 0.875rem;
    font-style: $form-feedback-font-style;
    color: $color;
    margin-top: -0.75rem;
  }

  .#{$state}-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%; // Contain to parent when possible
    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;
    margin-top: 0.1rem;
    font-size: 0.875rem;
    line-height: $form-feedback-tooltip-line-height;
    background-color: $tooltip-bg-color;
    border-radius: 0.25rem !important;
    color: $form-feedback-valid-tooltip-color;
  }

  @include form-validation-state-selector($state) {
    ~ .#{$state}-feedback,
    ~ .#{$state}-tooltip {
      display: block;
    }
  }

  .form-control {
    @include form-validation-state-selector($state) {
      margin-bottom: 1rem;
      background-image: none;
      border-color: $color;

      &:focus {
        border-color: $color;
        box-shadow: $focus-box-shadow;
      }
    }
  }

  .form-outline {
    .form-control {
      @include form-validation-state-selector($state) {
        ~ .form-label {
          color: $color;
        }

        ~ .form-notch .form-notch-leading,
        ~ .form-notch .form-notch-middle,
        ~ .form-notch .form-notch-trailing {
          border-color: $color;
        }

        &:focus ~ .form-notch .form-notch-middle,
        &.active ~ .form-notch .form-notch-middle {
          border-top: 1px solid transparent;
        }
        &:focus ~ .form-notch .form-notch-middle {
          box-shadow: 0 1px 0 0 $color;
        }
        &:focus ~ .form-notch .form-notch-leading {
          border-color: $color;
          box-shadow: -1px 0 0 0 $color, 0 1px 0 0 $color, 0 -1px 0 0 $color;
        }
        &:focus ~ .form-notch .form-notch-trailing {
          border-color: $color;
          box-shadow: 1px 0 0 0 $color, 0 -1px 0 0 $color, 0 1px 0 0 $color;
        }

        &.select-input.focused {
          & ~ .form-notch .form-notch-leading {
            box-shadow: -1px 0 0 0 $color, 0 1px 0 0 $color, 0 -1px 0 0 $color;
          }

          & ~ .form-notch .form-notch-middle {
            box-shadow: 0 1px 0 0 $color;
            border-top: 1px solid transparent;
          }

          & ~ .form-notch .form-notch-trailing {
            box-shadow: 1px 0 0 0 $color, 0 -1px 0 0 $color, 0 1px 0 0 $color;
          }
        }
      }
    }
  }

  .form-select {
    @include form-validation-state-selector($state) {
      border-color: $color;

      &:focus {
        border-color: $color;
        box-shadow: $focus-box-shadow;
      }

      ~ .#{$state}-feedback {
        margin-top: 0;
      }
    }
  }

  .input-group {
    .form-control {
      @include form-validation-state-selector($state) {
        margin-bottom: 0;
      }
    }
  }

  input[type='file'].form-control {
    @include form-validation-state-selector($state) {
      .#{$state}-feedback {
        margin-top: 0;
      }

      &:focus {
        box-shadow: inset 0 0 0 1px $color;
        border-color: $color;

        ~ .form-file-label {
          box-shadow: none;
        }
      }

      &:focus-within {
        ~ .form-file-label {
          .form-file-text,
          .form-file-button {
            border-color: $color;
          }
        }
      }
    }
  }

  .form-check-input {
    @include form-validation-state-selector($state) {
      border-color: $color;

      &:checked {
        background-color: $color;
      }

      &:checked {
        &:focus {
          &:before {
            box-shadow: 0px 0px 0px 13px $color;
          }
        }
      }

      &:focus {
        box-shadow: none;

        &:before {
          box-shadow: 0px 0px 0px 13px $color;
        }
      }

      ~ .form-check-label {
        color: $color;
        margin-bottom: 1rem;
      }

      &[type='checkbox'] {
        &:checked {
          &:focus {
            background-color: $color;
            border-color: $color;
          }
        }
      }

      &[type='radio'] {
        &:checked {
          border-color: $color;
          background-color: $white;

          &:focus {
            &:before {
              box-shadow: 0px 0px 0px 13px $color;
            }
          }

          &:after {
            border-color: $color;
            background-color: $color;
          }
        }
      }
    }
  }
  .form-check-inline .form-check-input {
    ~ .#{$state}-feedback {
      margin-left: 0.5em;
    }
  }

  .form-switch {
    .form-check-input {
      @include form-validation-state-selector($state) {
        &:focus {
          &:before {
            box-shadow: $form-switch-form-check-input-focus-before-box-shadow;
          }
        }
        &:checked {
          &[type='checkbox'] {
            &:after {
              background-color: $color;
              box-shadow: $form-switch-form-check-input-checked-checkbox-after-box-shadow;
            }
          }
          &:focus {
            &:before {
              box-shadow: 3px -1px 0px 13px $color;
            }
          }
        }
      }
    }
  }
}

.was-validated .input-group .invalid-feedback,
.was-validated .input-group .valid-feedback {
  margin-top: 2.5rem;
}

.input-group .invalid-feedback,
.input-group .valid-feedback {
  margin-top: 2.5rem;
}

@each $state, $data in $form-validation-states-mdb {
  @include form-validation-state-mdb($state, $data...);
}
