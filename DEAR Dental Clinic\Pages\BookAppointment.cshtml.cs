using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace DEAR_Dental_Clinic.Pages
{
    public class BookAppointmentModel : PageModel
    {
        private readonly ILogger<BookAppointmentModel> _logger;

        public BookAppointmentModel(ILogger<BookAppointmentModel> logger)
        {
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            [Required(ErrorMessage = "First name is required")]
            [Display(Name = "First Name")]
            public string FirstName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Last name is required")]
            [Display(Name = "Last Name")]
            public string LastName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Email is required")]
            [EmailAddress(ErrorMessage = "Please enter a valid email address")]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;

            [Required(ErrorMessage = "Phone number is required")]
            [Phone(ErrorMessage = "Please enter a valid phone number")]
            [Display(Name = "Phone")]
            public string Phone { get; set; } = string.Empty;

            [Required(ErrorMessage = "Please select a preferred date")]
            [Display(Name = "Preferred Date")]
            [DataType(DataType.Date)]
            public DateTime PreferredDate { get; set; } = DateTime.Today.AddDays(1);

            [Required(ErrorMessage = "Please select a preferred time")]
            [Display(Name = "Preferred Time")]
            public string PreferredTime { get; set; } = string.Empty;

            [Required(ErrorMessage = "Please select a service type")]
            [Display(Name = "Service Type")]
            public string ServiceType { get; set; } = string.Empty;

            [Display(Name = "Additional Notes")]
            public string Notes { get; set; } = string.Empty;

            [Display(Name = "New Patient")]
            public bool IsNewPatient { get; set; }
        }

        public void OnGet()
        {
            _logger.LogInformation("Book Appointment page accessed at {Time}", DateTime.Now);
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // TODO: Implement appointment booking logic here
                _logger.LogInformation("Appointment booking request from {Email} for {Date} at {Time}", 
                    Input.Email, Input.PreferredDate, Input.PreferredTime);
                
                // For now, just log the appointment details
                _logger.LogInformation("Appointment details: {FirstName} {LastName}, Service: {Service}, New Patient: {IsNew}", 
                    Input.FirstName, Input.LastName, Input.ServiceType, Input.IsNewPatient);
                
                // Add success message
                TempData["SuccessMessage"] = "Your appointment request has been submitted successfully! We will contact you within 24 hours to confirm your appointment.";
                
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing appointment booking");
                ModelState.AddModelError(string.Empty, "An error occurred while booking your appointment. Please try again.");
                return Page();
            }
        }
    }
}
