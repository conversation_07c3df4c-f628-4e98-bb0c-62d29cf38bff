@model DEAR_Dental_Clinic.Models.LoginViewModel
@{
    ViewData["Title"] = "Login - DEAR Dental Clinic";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="login-page">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-xl-4 col-lg-5 col-md-6 col-sm-8 col-12">
                <div class="login-card">
                    <div class="login-header text-center mb-4">
                        <div class="clinic-logo mb-3">
                            <i class="fas fa-tooth fa-3x text-primary"></i>
                        </div>
                        <h2 class="login-title">DEAR Dental Clinic</h2>
                        <p class="login-subtitle">Sign in to your account</p>
                    </div>

                    <form method="post" class="login-form">
                        @Html.AntiForgeryToken()
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="Email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input asp-for="Email" class="form-control" placeholder="Enter your email" autocomplete="username" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input asp-for="Password" type="password" class="form-control" placeholder="Enter your password" autocomplete="current-password" />
                                <button type="button" class="btn btn-outline-secondary toggle-password" data-target="Password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input asp-for="RememberMe" class="form-check-input" type="checkbox" />
                                <label asp-for="RememberMe" class="form-check-label">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary w-100 login-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </div>

                        <div class="login-links text-center">
                            <a href="#" class="forgot-password-link">Forgot your password?</a>
                        </div>
                    </form>

                    <div class="login-footer mt-4">
                        <div class="divider">
                            <span>or</span>
                        </div>
                        <div class="text-center mt-3">
                            <p class="mb-2">Don't have an account?</p>
                            <a href="#" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Access for Staff -->
                <div class="staff-access mt-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="card-title">Staff Access</h6>
                            <div class="row">
                                <div class="col-6">
                                    <button type="button" class="btn btn-sm btn-outline-info w-100 mb-2">
                                        <i class="fas fa-user-md"></i><br>
                                        <small>Doctor</small>
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-sm btn-outline-success w-100 mb-2">
                                        <i class="fas fa-user-nurse"></i><br>
                                        <small>Staff</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script>
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordInput = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
    </script>
}
