//
// Material styles for form control - form outline
//

.form-control {
  min-height: auto;
  padding: 4.5px 12px 3.68px 12px;
  transition: all 0.1s linear;
  box-shadow: none;

  &:focus {
    box-shadow: none;
    transition: all 0.1s linear;
    border-color: $primary;
    box-shadow: inset 0px 0px 0px 1px $primary;
  }
  &.form-control-sm {
    font-size: 0.775rem;
    line-height: 1.5;
  }
  &.form-control-lg {
    line-height: 2.15;
    border-radius: 0.25rem;
  }
}

.form-outline {
  position: relative;
  width: 100%;

  .form-helper {
    width: 100%;
    position: absolute;
    font-size: 0.875em;
    color: #757575;
    .form-counter {
      text-align: right;
    }
  }

  .trailing {
    position: absolute;
    right: 10px;
    left: initial;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--#{$prefix}surface-color);
  }

  .form-icon-trailing {
    padding-right: 2rem !important;
  }

  .form-control {
    min-height: auto;
    padding-top: $input-padding-top;
    padding-bottom: $input-padding-bottom;
    padding-left: $input-padding-left;
    padding-right: $input-padding-right;
    border: 0;
    background: transparent;
    transition: $input-transition;
    ~ .form-label {
      position: absolute;
      top: 0;
      max-width: 90%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      left: $form-label-left;
      padding-top: $form-label-padding-top;
      pointer-events: none;
      transform-origin: 0 0;
      transition: $form-label-transition;
      color: $form-label-color;
      margin-bottom: 0;
    }
    ~ .form-notch {
      display: flex;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      max-width: 100%;
      height: 100%;
      text-align: left;
      pointer-events: none;
      div {
        pointer-events: none;
        border: $border-width solid;
        border-color: $form-notch-div-border-color;
        box-sizing: border-box;
        background: transparent;
        transition: $input-transition;
      }
      .form-notch-leading {
        left: 0;
        top: 0;
        height: 100%;
        width: $form-notch-leading-width;
        border-right: none;
        border-radius: $form-notch-leading-border-radius 0 0 $form-notch-leading-border-radius;
      }
      .form-notch-middle {
        flex: 0 0 auto;
        width: auto;
        max-width: calc(100% - #{$form-notch-middle-max-width});
        height: 100%;
        border-right: none;
        border-left: none;
      }
      .form-notch-trailing {
        flex-grow: 1;
        height: 100%;
        border-left: none;
        border-radius: 0 $form-notch-trailing-border-radius $form-notch-trailing-border-radius 0;
      }
    }
    &:not(.placeholder-active)::placeholder {
      opacity: 0;
    }
    &:focus,
    &.active {
      &::placeholder {
        opacity: 1;
      }
    }
    &:focus {
      box-shadow: none !important;
    }
    &:focus ~ .form-label,
    &.active ~ .form-label {
      transform: $input-focus-active-label-transform;
    }
    &:focus ~ .form-label {
      color: $input-focus-label-color;
    }
    &:focus ~ .form-notch .form-notch-middle,
    &.active ~ .form-notch .form-notch-middle {
      border-right: none;
      border-left: none;
      border-top: 1px solid transparent;
    }
    &:focus ~ .form-notch .form-notch-middle {
      border-color: $input-focus-border-color;
      box-shadow: 0 1px 0 0 $input-focus-border-color;
      border-top: 1px solid transparent;
    }
    &:focus ~ .form-notch .form-notch-leading,
    &.active ~ .form-notch .form-notch-leading {
      border-right: none;
    }
    &:focus ~ .form-notch .form-notch-leading {
      border-color: $input-focus-border-color;
      box-shadow: -1px 0 0 0 $input-focus-border-color, 0 1px 0 0 $input-focus-border-color,
        0 -1px 0 0 $input-focus-border-color;
    }
    &:focus ~ .form-notch .form-notch-trailing,
    &.active ~ .form-notch .form-notch-trailing {
      border-left: none;
    }
    &:focus ~ .form-notch .form-notch-trailing {
      border-color: $input-focus-border-color;
      box-shadow: 1px 0 0 0 $input-focus-border-color, 0 -1px 0 0 $input-focus-border-color,
        0 1px 0 0 $input-focus-border-color;
    }
    &:disabled,
    &.disabled,
    &[readonly] {
      background-color: $input-disabled-background-color;
    }

    &:disabled,
    &.disabled,
    &[readonly] {
      ~ .timepicker-toggle-button,
      ~ .datepicker-toggle-button,
      ~ .datetimepicker-toggle-button,
      ~ .select-arrow,
      ~ .trailing {
        color: rgba(var(--#{$prefix}surface-color-rgb), 0.5);
      }
    }

    &.form-control-lg {
      font-size: $input-font-size-lg;
      line-height: $input-line-height-lg;
      // padding-left: $input-padding-left-lg;
      // padding-right: $input-padding-right-lg;
      ~ .form-label {
        padding-top: $form-label-padding-top-lg;
      }
      &:focus ~ .form-label,
      &.active ~ .form-label {
        transform: $input-focus-active-label-transform-lg;
      }
    }
    &.form-control-sm {
      // padding-left: $input-padding-left-sm;
      // padding-right: $input-padding-right-sm;
      padding-top: $input-padding-top-sm;
      padding-bottom: $input-padding-bottom-sm;
      font-size: $input-font-size-sm;
      line-height: $input-line-height-sm;
      ~ .form-label {
        padding-top: $form-label-padding-top-sm;
        font-size: $form-label-font-size-sm;
      }
      &:focus ~ .form-label,
      &.active ~ .form-label {
        transform: $input-focus-active-label-transform-sm;
      }
    }
  }

  &.form-white {
    .form-control {
      color: $form-white-input-color;
      ~ .form-label {
        color: $form-white-label-color;
      }
      ~ .form-notch {
        div {
          border-color: $form-white-notch-div-border-color;
        }
      }
      &:focus ~ .form-label {
        color: $form-white-input-focus-label-color;
      }
      &:focus ~ .form-notch .form-notch-middle {
        border-color: $form-white-input-focus-border-color;
        box-shadow: 0 1px 0 0 $form-white-input-focus-border-color;
        border-top: 1px solid transparent;
      }
      &:focus ~ .form-notch .form-notch-leading {
        border-color: $form-white-input-focus-border-color;
        box-shadow: -1px 0 0 0 $form-white-input-focus-border-color,
          0 1px 0 0 $form-white-input-focus-border-color,
          0 -1px 0 0 $form-white-input-focus-border-color;
      }
      &:focus ~ .form-notch .form-notch-trailing {
        border-color: $form-white-input-focus-border-color;
        box-shadow: 1px 0 0 0 $form-white-input-focus-border-color,
          0 -1px 0 0 $form-white-input-focus-border-color,
          0 1px 0 0 $form-white-input-focus-border-color;
      }
      &::placeholder {
        color: $form-white-placeholder-color;
      }
      &:disabled,
      &.disabled,
      &[readonly] {
        background-color: $form-white-disabled-bgc;
      }
    }
  }
}
