﻿using System;
using System.Data;
using Microsoft.Data.SqlClient;

namespace DEAR_Dental_Clinic.Scripts.Helper
{
    public class DatabaseHelper
    {
        private string connectionString;
        public DatabaseHelper()
        {
            connectionString = new SqlConnectionStringBuilder
            {
                DataSource = @"*************,49170",           // SQL Server instance name
                InitialCatalog = "DEAR_DentalManagementSystem",   // Database name
                IntegratedSecurity = true,              // Use Windows Authentication
                TrustServerCertificate = true           // Only for testing or if you have a self-signed cert you've manually trusted
            }.ConnectionString;
        }
        public SqlConnection GetConnection()
        {
            return new SqlConnection(connectionString);
        }

        public bool TestConnection()
        {
            try
            {
                using (SqlConnection connection = GetConnection())
                {
                    connection.Open();
                    Console.Write("Connection Successful!");
                    return true;
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine("Connection Error: " + ex.Message);
                return false;
            }
        }

    }

}
