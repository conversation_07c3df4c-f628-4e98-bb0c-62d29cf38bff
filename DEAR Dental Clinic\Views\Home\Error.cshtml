@{
    ViewData["Title"] = "Error - DEAR Dental Clinic";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-content">
                <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
                <h1 class="display-4">Oops!</h1>
                <h2 class="mb-4">Something went wrong</h2>
                <p class="lead">We're sorry, but an error occurred while processing your request.</p>
                <div class="mt-4">
                    <a href="/" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>Go Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
