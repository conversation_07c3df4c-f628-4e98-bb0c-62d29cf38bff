// CORE FUNCTIONS
@import './bootstrap-rtl-fix/functions';
@import './free/functions';

// CORE VARIABLES
@import './custom/variables';
@import './free/variables';
@import './free/variables-dark';
@import './bootstrap-rtl-fix/variables';
@import './bootstrap-rtl-fix/variables-dark';
@import './bootstrap-rtl-fix/maps';

// BOOTSTRAP CORE
@import './bootstrap-rtl-fix/mixins';
@import './bootstrap-rtl-fix/utilities';

// BOOTSTRAP CORE COMPONENTS
@import './bootstrap-rtl-fix/root';
@import './bootstrap-rtl-fix/reboot';
@import './bootstrap-rtl-fix/type';
@import './bootstrap-rtl-fix/images';
@import './bootstrap-rtl-fix/containers';
@import './bootstrap-rtl-fix/grid';

// BOOTSTRAP COMPONENTS
@import './bootstrap-rtl-fix/tables';
@import './bootstrap-rtl-fix/forms';
@import './bootstrap-rtl-fix/buttons';
@import './bootstrap-rtl-fix/transitions';
@import './bootstrap-rtl-fix/dropdown';
@import './bootstrap-rtl-fix/button-group';
@import './bootstrap-rtl-fix/nav';
@import './bootstrap-rtl-fix/navbar';
@import './bootstrap-rtl-fix/card';
@import './bootstrap-rtl-fix/accordion';
@import './bootstrap-rtl-fix/breadcrumb';
@import './bootstrap-rtl-fix/pagination';
@import './bootstrap-rtl-fix/badge';
@import './bootstrap-rtl-fix/alert';
@import './bootstrap-rtl-fix/progress';
@import './bootstrap-rtl-fix/list-group';
@import './bootstrap-rtl-fix/close';
@import './bootstrap-rtl-fix/toasts';
@import './bootstrap-rtl-fix/modal';
@import './bootstrap-rtl-fix/tooltip';
@import './bootstrap-rtl-fix/popover';
@import './bootstrap-rtl-fix/carousel';
@import './bootstrap-rtl-fix/spinners';
@import './bootstrap-rtl-fix/offcanvas';
@import './bootstrap-rtl-fix/placeholders';

// Helpers
@import './bootstrap-rtl-fix/helpers';

// Utilities
@import './free/utilities';
@import './bootstrap-rtl-fix/utilities/api';

// MDB CORE
@import './free/mixins';
@import './free/utilities';

// MDB CORE COMPONENTS
@import './free/root';
@import './free/reboot';
@import './free/type';
@import './free/colors';
@import './free/shadows';
@import './free/flag';
@import './free/images';

// MDB FORMS
@import './free/forms/form-control';
@import './free/forms/form-select';
@import './free/forms/form-check';
@import './free/forms/form-file';
@import './free/forms/input-group';
@import './free/forms/validation';
@import './free/forms/form-range';

// MDB COMPONENTS
@import './free/tables';
@import './free/buttons';
@import './free/deprecated';
@import './free/dropdown';
@import './free/button-group';
@import './free/nav';
@import './free/navbar';
@import './free/card';
@import './free/breadcrumb';
@import './free/pagination';
@import './free/badge';
@import './free/alert';
@import './free/progress';
@import './free/list-group';
@import './free/close';
@import './free/modal';
@import './free/toasts';
@import './free/tooltip';
@import './free/popover';
@import './free/scrollspy';
@import './free/ripple';
@import './free/range';
@import './free/accordion';
@import './free/carousel';
