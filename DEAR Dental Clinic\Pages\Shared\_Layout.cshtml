﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - DEAR_Dental_Clinic</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/DEAR_Dental_Clinic.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom box-shadow">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-page="/Index">
                    <i class="fas fa-tooth text-primary me-2"></i>
                    DEAR Dental Clinic
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/Index">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/Services">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/About">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-page="/Contact">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary text-white ms-2 px-3" asp-area="" asp-page="/Login">Login</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main">
        @RenderBody()
    </main>

    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-tooth me-2"></i>DEAR Dental Clinic</h5>
                    <p>Your trusted partner for comprehensive dental care.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; @DateTime.Now.Year DEAR Dental Clinic. All rights reserved.</p>
                    <a asp-area="" asp-page="/Privacy" class="text-light">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
