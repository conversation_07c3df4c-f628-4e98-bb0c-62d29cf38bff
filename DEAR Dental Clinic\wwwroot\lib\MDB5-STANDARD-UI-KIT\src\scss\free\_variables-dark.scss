// Dark color mode variables
//
// Custom variables for the `[data-bs-theme="dark"]` theme. Use this as a starting point for your own custom color modes by creating a new theme-specific file like `_variables-dark.scss` and adding the variables you need.

//
// Global colors
//

// scss-docs-start sass-dark-mode-vars

// scss-docs-start theme-text-variables
$primary-text-emphasis-dark: tint-color($primary, 20%) !default;
$secondary-text-emphasis-dark: tint-color($secondary, 60%) !default;
$success-text-emphasis-dark: tint-color($success, 40%) !default;
$info-text-emphasis-dark: tint-color($info, 30%) !default;
$warning-text-emphasis-dark: tint-color($warning, 40%) !default;
$danger-text-emphasis-dark: tint-color($danger, 20%) !default;
$light-text-emphasis-dark: $gray-100 !default;
$dark-text-emphasis-dark: $gray-200 !default;
// scss-docs-end theme-text-variables

// scss-docs-start theme-bg-subtle-variables
$primary-bg-subtle-dark: shade-color($primary, 80%) !default;
$secondary-bg-subtle-dark: shade-color($secondary, 80%) !default;
$success-bg-subtle-dark: shade-color($success, 80%) !default;
$info-bg-subtle-dark: shade-color($info, 80%) !default;
$warning-bg-subtle-dark: shade-color($warning, 80%) !default;
$danger-bg-subtle-dark: shade-color($danger, 80%) !default;
$light-bg-subtle-dark: $gray-800 !default;
$dark-bg-subtle-dark: $gray-900 !default;
// scss-docs-end theme-bg-subtle-variables

// scss-docs-start theme-border-subtle-variables
$primary-border-subtle-dark: shade-color($primary, 40%) !default;
$secondary-border-subtle-dark: shade-color($secondary, 40%) !default;
$success-border-subtle-dark: shade-color($success, 40%) !default;
$info-border-subtle-dark: shade-color($info, 40%) !default;
$warning-border-subtle-dark: shade-color($warning, 40%) !default;
$danger-border-subtle-dark: shade-color($danger, 40%) !default;
$light-border-subtle-dark: $gray-700 !default;
$dark-border-subtle-dark: $gray-800 !default;
// scss-docs-end theme-border-subtle-variables

$body-color-dark: $white !default;
$body-bg-dark: #303030 !default;
$body-secondary-bg-dark: $gray-800 !default;
$body-tertiary-bg-dark: mix($gray-800, $gray-900, 50%) !default;
$border-color-dark: rgba(255, 255, 255, 0.12) !default;
$border-color-translucent-dark: rgba($white, 0.15) !default;

// Global MDB dark theme variables

// scss-docs-start mdb-global-dark-theme-variables
$surface-color-dark: $white !default;
$surface-bg-dark: #424242 !default;
$surface-inverted-color-dark: $white !default;
$surface-inverted-bg-dark: #757575 !default;
$divider-color-dark: rgba(255, 255, 255, 0.12) !default;
$divider-blurry-color-dark: hsl(0, 0%, 70%) !default;
$highlight-bg-color-dark: tint-color($gray-900, 10%) !default;
$scrollbar-rail-bg-dark: $gray-500 !default;
$scrollbar-thumb-bg-dark: $gray-200 !default;
$picker-header-bg-dark: #323232 !default;
$timepicker-clock-face-bg-dark: $gray-700 !default;
$sidenav-backdrop-opacity-dark: 0.5 !default;
$form-control-border-color-dark: rgba(255, 255, 255, 0.7) !default;
$form-control-label-color-dark: $gray-400 !default;
$form-control-disabled-bg-dark: $gray-700 !default;
$box-shadow-color-dark: $black !default;
$stepper-mobile-bg-dark: $body-tertiary-bg-dark !default;
$datepicker-cell-in-range-bg-dark: $gray-700 !default;
// scss-docs-start mdb-global-dark-theme-variables

// Accordion

// scss-docs-start accordion-variables
$accordion-icon-color-dark: $surface-color-dark !default;
$accordion-icon-active-color-dark: $primary !default;
// scss-docs-end accordion-variables
