namespace DEAR_Dental_Clinic.Scripts.Seed
{
	public class Seeds
	{
		public static List<User> Users { get; private set; } = new List<User>();
		public static void SeedUseres()
		{
			string adminUsername = "admin";
			string doctorUsername = "doctor";

			bool usersExists = Users.Any(user => user.Username == adminUsername || user.Username == doctorUsername);
			if (!usersExists)
			{
				var adminUser = new User
				{
					Username = "admin",
					Password = "admin",
					Role = "Admin"
				};

				var doctorUser = new User
				{
					Username = "doctor",
					Password = "doctor",
					Role = "Doctor"
				};

				Users.Add(adminUser);
				Users.Add(doctorUser);
				Console.WriteLine("Accounts created successfully.");
			}
			else
			{ 
				Console.WriteLine("Accounts already exists.");
			}
		}
	}
	public class User
	{
		public string Username { get; set; }
		public string Password { get; set; }
		public string Role { get; set; }

	}
}