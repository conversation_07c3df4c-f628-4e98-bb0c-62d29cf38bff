using System.Security.Cryptography.Xml;

namespace Dear_Dental_Clinic.Scripts.OOP
{

	namespace DEAR_Dental_Clinic.Scripts.Overall_Scripts
	{

		public class MedicalHistory
		{
			public int MedicalHistoryID { get; set; }
			public string? PhysicianName { get; set; }
			public string? SpecialtyIfAvailable { get; set; }
			public string? OfficeAddress { get; set; }
			public string? OfficeNumber { get; set; }
			public bool? Q1 { get; set; }
			public bool? Q2 { get; set; }
			public string? Q2sub { get; set; }
			public bool? Q3 { get; set; }
			public string? Q3sub { get; set; }
			public bool? Q4 { get; set; }
			public string? Q4sub { get; set; }
			public bool? Q5 { get; set; }
			public string? Q5sub { get; set; }
			public bool? Q6 { get; set; }
			public bool? Q7 { get; set; }
			public string? Q8 { get; set; }
			public string? Q8sub { get; set; }
			public string? Q9 { get; set; }
			public string? Q10 { get; set; }
			public string? Q11 { get; set; }
			public string? Q12 { get; set; }
			public string? Q13 { get; set; }
			public byte[]? Signature { get; set; } 
			public int PatientID { get; set; } // This is for calling a foreign key
		}
	}

}
