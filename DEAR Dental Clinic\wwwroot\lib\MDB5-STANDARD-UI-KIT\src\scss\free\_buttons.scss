//
// Button base styles
//

.btn {
  // scss-docs-start btn-css-vars
  --#{$prefix}btn-padding-top: #{$btn-padding-top};
  --#{$prefix}btn-padding-bottom: #{$btn-padding-bottom};
  --#{$prefix}btn-border-width: 0;
  --#{$prefix}btn-border-color: none;
  --#{$prefix}btn-border-radius: #{$btn-border-radius};
  --#{$prefix}btn-box-shadow: #{$btn-box-shadow};
  --#{$prefix}btn-hover-box-shadow: #{$btn-hover-box-shadow};
  --#{$prefix}btn-focus-box-shadow: #{$btn-focus-box-shadow};
  --#{$prefix}btn-active-box-shadow: #{$btn-active-box-shadow};
  // scss-docs-end btn-css-vars

  padding-top: var(--#{$prefix}btn-padding-top);
  padding-bottom: var(--#{$prefix}btn-padding-bottom);
  text-transform: uppercase;
  vertical-align: bottom;
  border: 0;
  @include border-radius(var(--#{$prefix}btn-border-radius));
  box-shadow: var(--#{$prefix}btn-box-shadow);

  :not(.btn-check) + &:hover,
  &:first-child:hover,
  &:focus-visible,
  &:hover {
    box-shadow: var(--#{$prefix}btn-hover-box-shadow);
  }

  .btn-check:focus-visible + &,
  .btn-check:focus + &,
  &:focus {
    box-shadow: var(--#{$prefix}btn-focus-box-shadow);
  }

  .btn-check:checked + &,
  .btn-check:active + &,
  &:active,
  &.active,
  &.show {
    box-shadow: var(--#{$prefix}btn-active-box-shadow);

    &:focus {
      box-shadow: var(--#{$prefix}btn-focus-box-shadow);
    }
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }
}

//
// Outline styles
//

[class*='btn-outline-'] {
  // scss-docs-start btn-outline-css-vars
  --#{$prefix}btn-padding-top: #{$btn-outline-padding-top};
  --#{$prefix}btn-padding-bottom: #{$btn-outline-padding-bottom};
  --#{$prefix}btn-padding-x: #{$btn-outline-padding-x};
  --#{$prefix}btn-border-width: #{$btn-outline-border-width};
  --#{$prefix}btn-line-height: #{$btn-line-height};
  // scss-docs-end btn-outline-css-vars

  padding: var(--#{$prefix}btn-padding-top) var(--#{$prefix}btn-padding-x)
    var(--#{$prefix}btn-padding-bottom);
  border-width: var(--#{$prefix}btn-border-width);
  border-style: solid;
  box-shadow: none;

  :not(.btn-check) + &:hover,
  &:first-child:hover,
  &:focus-visible,
  &:hover {
    box-shadow: none;
  }

  .btn-check:focus-visible + &,
  .btn-check:focus + &,
  &:focus {
    box-shadow: none;
  }

  .btn-check:checked + &,
  .btn-check:active + &,
  &:active,
  &.active,
  &.show {
    box-shadow: none;

    &:focus {
      box-shadow: none;
    }
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: none;
  }

  &.btn-lg {
    @include button-outline-size(
      $btn-outline-padding-top-lg,
      $btn-outline-padding-bottom-lg,
      $btn-outline-padding-x-lg,
      $btn-outline-font-size-lg,
      $btn-outline-line-height-lg
    );
  }

  &.btn-sm {
    @include button-outline-size(
      $btn-outline-padding-top-sm,
      $btn-outline-padding-bottom-sm,
      $btn-outline-padding-x-sm,
      $btn-outline-font-size-sm,
      $btn-outline-line-height-sm
    );
  }
}

//
// Alternate buttons
//

// scss-docs-start btn-secondary
.btn-secondary {
  box-shadow: none;

  :not(.btn-check) + &:hover,
  &:first-child:hover,
  &:focus-visible,
  &:hover {
    box-shadow: none !important;
  }

  .btn-check:focus-visible + &,
  .btn-check:focus + &,
  &:focus {
    box-shadow: none;
  }

  .btn-check:checked + &,
  .btn-check:active + &,
  &:active,
  &.active,
  &.show {
    box-shadow: none;

    &:focus {
      box-shadow: none;
    }
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: none;
  }
}
// scss-docs-end btn-secondary

// scss-docs-start btn-variant-loop
@each $color, $value in $theme-colors {
  .btn-#{$color} {
    @if $color == 'secondary' {
      @include button-variant(
        tint-color($primary, $btn-secondary-bg-tint-amount),
        $value,
        $color: shade-color($primary, $btn-secondary-color-shade-amount),
        $hover-background:
          shade-color(
            tint-color($primary, $btn-secondary-bg-tint-amount),
            $btn-hover-bg-shade-amount
          ),
        $focus-background:
          shade-color(
            tint-color($primary, $btn-secondary-bg-tint-amount),
            $btn-hover-bg-shade-amount
          ),
        $active-background:
          shade-color(
            tint-color($primary, $btn-secondary-bg-tint-amount),
            $btn-hover-bg-shade-amount
          )
      );
    } @else if $color == 'light' {
      @include button-variant(
        $light-bg-subtle,
        $light-border-subtle,
        $color: $light-text-emphasis,
        $hover-background: shade-color($light-bg-subtle, $btn-hover-bg-shade-amount),
        $hover-border: shade-color($light-border-subtle, $btn-hover-border-shade-amount),
        $active-background: shade-color($light-bg-subtle, $btn-active-bg-shade-amount),
        $active-border: shade-color($light-border-subtle, $btn-active-border-shade-amount)
      );
    } @else if $color == 'dark' {
      @include button-variant(
        $dark-bg-subtle,
        $dark-border-subtle,
        $color: $dark-text-emphasis,
        $hover-background: tint-color($dark-bg-subtle, $btn-hover-bg-tint-amount),
        $hover-border: tint-color($dark-border-subtle, $btn-hover-border-tint-amount),
        $active-background: tint-color($dark-bg-subtle, $btn-active-bg-tint-amount),
        $active-border: tint-color($dark-border-subtle, $btn-active-border-tint-amount)
      );
    } @else {
      @include button-variant($value, $value);
    }

    @if $color == 'secondary' {
      --#{$prefix}btn-box-shadow-state: transparent;
    } @else {
      --#{$prefix}btn-box-shadow-state: #{$btn-contextual-box-shadow-state-first-part
          rgba(shade-color($value, $btn-hover-bg-shade-amount), 0.3),
        $btn-contextual-box-shadow-state-second-part
          rgba(shade-color($value, $btn-hover-bg-shade-amount), 0.2)};
    }

    :not(.btn-check) + &:hover,
    &:first-child:hover,
    &:focus-visible,
    &:hover {
      box-shadow: var(--#{$prefix}btn-box-shadow-state);
    }

    .btn-check:focus-visible + &,
    .btn-check:focus + &,
    &:focus {
      box-shadow: var(--#{$prefix}btn-box-shadow-state);
      background-color: var(--#{$prefix}btn-focus-bg);
    }

    .btn-check:checked + &,
    .btn-check:active + &,
    &:active,
    &.active,
    &.show {
      box-shadow: var(--#{$prefix}btn-box-shadow-state);

      &:focus {
        box-shadow: var(--#{$prefix}btn-box-shadow-state);
      }

      &:hover {
        background-color: var(--#{$prefix}btn-active-bg);
      }
    }

    &:disabled,
    &.disabled,
    fieldset:disabled & {
      box-shadow: var(--#{$prefix}btn-box-shadow);
    }
  }

  [data-mdb-theme='dark'] .btn-#{$color} {
    @if $color != 'secondary' {
      box-shadow: 0 4px 9px -4px rgba($black, 0.35);
      &:hover,
      &:active,
      &:focus {
        box-shadow: 0 4px 18px -2px rgba($black, 0.7);
      }
    }
  }
}

// scss-docs-end btn-variant-loop

// scss-docs-start btn-outline-variant-loop
@each $color, $value in $theme-colors {
  .btn-outline-#{$color} {
    @if $color == 'secondary' {
      @include button-outline-variant(
        shade-color($primary, $btn-secondary-color-shade-amount),
        shade-color($primary, $btn-secondary-color-shade-amount),
        shade-color($primary, $btn-secondary-color-shade-amount),
        shade-color($primary, $btn-secondary-color-shade-amount)
      );

      --#{$prefix}btn-outline-border-color: #{tint-color($primary, $btn-secondary-bg-tint-amount)};
      --#{$prefix}btn-outline-focus-border-color: #{shade-color(
          tint-color($primary, $btn-secondary-bg-tint-amount),
          $btn-focus-bg-shade-amount
        )};
      --#{$prefix}btn-outline-hover-border-color: #{shade-color(
          tint-color($primary, $btn-secondary-bg-tint-amount),
          $btn-hover-bg-shade-amount
        )};

      border-color: var(--#{$prefix}btn-outline-border-color);
    } @else {
      @include button-outline-variant(
        $value,
        shade-color($value, $btn-hover-bg-shade-amount),
        shade-color($value, $btn-focus-bg-shade-amount),
        shade-color($value, $btn-active-bg-shade-amount)
      );

      --#{$prefix}btn-outline-border-color: #{$value};
      --#{$prefix}btn-outline-focus-border-color: #{shade-color(
          $value,
          $btn-outline-focus-bg-shade-amount
        )};
      --#{$prefix}btn-outline-hover-border-color: #{shade-color(
          $value,
          $btn-outline-hover-bg-shade-amount
        )};

      border-color: var(--#{$prefix}btn-outline-border-color);
    }

    :not(.btn-check) + &:hover,
    &:first-child:hover,
    &:focus-visible,
    &:hover {
      border-color: var(--#{$prefix}btn-outline-hover-border-color);
    }

    .btn-check:focus-visible + &,
    .btn-check:focus + &,
    &:focus {
      border-color: var(--#{$prefix}btn-outline-focus-border-color);
    }

    .btn-check:checked + &,
    .btn-check:active + &,
    &:active,
    &.active,
    &.show {
      border-color: var(--#{$prefix}btn-outline-active-border-color);

      &:focus {
        border-color: var(--#{$prefix}btn-outline-focus-border-color);
      }
    }

    &:disabled,
    &.disabled,
    fieldset:disabled & {
      border-color: var(--#{$prefix}btn-outline-border-color);
    }
  }

  [data-mdb-theme='dark'] .btn-outline-#{$color} {
    @if $color == 'secondary' {
      @include button-outline-variant(
        tint-color($primary, 70%),
        tint-color($primary, 60%),
        tint-color($primary, 60%),
        tint-color($primary, 60%),
        shade-color($primary, 60%),
        shade-color($primary, 60%),
        shade-color($primary, 60%)
      );

      --#{$prefix}btn-outline-border-color: #{tint-color($primary, 50%)};
      --#{$prefix}btn-outline-focus-border-color: #{shade-color(
          tint-color($primary, 50%),
          $btn-focus-bg-shade-amount
        )};
      --#{$prefix}btn-outline-hover-border-color: #{shade-color(
          tint-color($primary, 50%),
          $btn-hover-bg-shade-amount
        )};

      border-color: var(--#{$prefix}btn-outline-border-color);
    } @else {
      @include button-outline-variant(
        tint-color($value, 20%),
        shade-color($value, $btn-hover-bg-shade-amount),
        shade-color($value, $btn-focus-bg-shade-amount),
        shade-color($value, $btn-active-bg-shade-amount),
        shade-color($value, 70%),
        shade-color($value, 70%),
        shade-color($value, 70%)
      );
    }
  }
}

// scss-docs-end btn-outline-variant-loop

//
// Link buttons
//

// Make a button look and behave like a link
.btn-link {
  --#{$prefix}btn-font-weight: #{$font-weight-medium};
  --#{$prefix}btn-color: #{$btn-link-color};
  --#{$prefix}btn-hover-color: #{$btn-link-hover-color};
  --#{$prefix}btn-hover-bg: #{$btn-link-hover-bg};
  --#{$prefix}btn-focus-color: #{$btn-link-focus-color};
  --#{$prefix}btn-active-color: #{$btn-link-active-color};
  --#{$prefix}btn-disabled-color: #{$btn-link-disabled-color};
  --#{$prefix}btn-box-shadow: none;

  text-decoration: $link-decoration;
  box-shadow: var(--#{$prefix}btn-box-shadow);

  :not(.btn-check) + &:hover,
  &:first-child:hover,
  &:focus-visible,
  &:hover {
    text-decoration: $link-hover-decoration;
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }

  .btn-check:focus-visible + &,
  .btn-check:focus + &,
  &:focus {
    color: var(--#{$prefix}btn-focus-color);
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }

  .btn-check:checked + &,
  .btn-check:active + &,
  &:active,
  &.active,
  &.show {
    color: var(--#{$prefix}btn-active-color);
    box-shadow: var(--#{$prefix}btn-box-shadow);

    &:focus {
      color: var(--#{$prefix}btn-focus-color);
      box-shadow: var(--#{$prefix}btn-box-shadow);
    }
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }
}

.btn-tertiary {
  --#{$prefix}btn-font-weight: #{$font-weight-medium};
  --#{$prefix}btn-color: #{$btn-link-color};
  --#{$prefix}btn-hover-color: #{$btn-link-hover-color};
  --#{$prefix}btn-hover-bg: transparent;
  --#{$prefix}btn-focus-color: #{$btn-link-focus-color};
  --#{$prefix}btn-active-color: #{$btn-link-active-color};
  --#{$prefix}btn-disabled-color: #{$btn-link-disabled-color};
  --#{$prefix}btn-box-shadow: none;

  padding-left: 0px;
  padding-right: 0px;
  text-decoration: $link-decoration;
  box-shadow: var(--#{$prefix}btn-box-shadow);

  :not(.btn-check) + &:hover,
  &:first-child:hover,
  &:focus-visible,
  &:hover {
    text-decoration: $link-hover-decoration;
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }

  .btn-check:focus-visible + &,
  .btn-check:focus + &,
  &:focus {
    color: var(--#{$prefix}btn-focus-color);
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }

  .btn-check:checked + &,
  .btn-check:active + &,
  &:active,
  &.active,
  &.show {
    color: var(--#{$prefix}btn-active-color);
    box-shadow: var(--#{$prefix}btn-box-shadow);

    &:focus {
      color: var(--#{$prefix}btn-focus-color);
      box-shadow: var(--#{$prefix}btn-box-shadow);
    }
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }
}

[data-mdb-theme='dark'] .btn-secondary {
  --#{$prefix}btn-bg: #{tint-color($primary, 60%)};
  --#{$prefix}btn-hover-bg: #{tint-color($primary, 50%)};
  --#{$prefix}btn-focus-bg: #{tint-color($primary, 50%)};
  --#{$prefix}btn-active-bg: #{tint-color($primary, 50%)};
}

[data-mdb-theme='dark'] .btn-link {
  --#{$prefix}btn-color: #{$secondary};
  --#{$prefix}btn-hover-color: #{tint-color($secondary, 30%)};
  --#{$prefix}btn-hover-bg: #{shade-color($secondary, 60%)};
  --#{$prefix}btn-focus-color: #{tint-color($secondary, 30%)};
  --#{$prefix}btn-active-color: #{tint-color($secondary, 30%)};
}

[data-mdb-theme='dark'] .btn-tertiary {
  --#{$prefix}btn-color: #{$secondary};
  --#{$prefix}btn-hover-color: #{tint-color($secondary, 30%)};
  --#{$prefix}btn-focus-color: #{tint-color($secondary, 30%)};
  --#{$prefix}btn-active-color: #{tint-color($secondary, 30%)};
}

//
// Button Sizes
//

.btn-lg {
  @include button-size(
    $btn-padding-top-lg,
    $btn-padding-bottom-lg,
    $btn-padding-x-lg,
    $btn-font-size-lg,
    $btn-line-height-lg
  );
}

.btn-sm {
  @include button-size(
    $btn-padding-top-sm,
    $btn-padding-bottom-sm,
    $btn-padding-x-sm,
    $btn-font-size-sm,
    $btn-line-height-sm
  );
}

//
// Rounded option
//

.btn-rounded {
  --#{$prefix}btn-border-radius: #{$btn-rounded-border-radius};

  border-radius: var(--#{$prefix}btn-border-radius);
}

//
// Floating option
//

.btn-floating,
[class*='btn-outline-'].btn-floating {
  --#{$prefix}btn-border-radius: #{$btn-floating-border-radius};

  border-radius: var(--#{$prefix}btn-border-radius);
  padding: 0;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-floating {
  --#{$prefix}btn-width: #{$btn-floating-width};
  --#{$prefix}btn-height: #{$btn-floating-height};
  --#{$prefix}btn-icon-width: #{$btn-floating-icon-width};
  --#{$prefix}btn-icon-line-height: #{$btn-floating-icon-line-height};
  --#{$prefix}btn-width-lg: #{$btn-floating-width-lg};
  --#{$prefix}btn-height-lg: #{$btn-floating-height-lg};
  --#{$prefix}btn-icon-width-lg: #{$btn-floating-icon-width-lg};
  --#{$prefix}btn-icon-line-height-lg: #{$btn-floating-icon-line-height-lg};
  --#{$prefix}btn-width-sm: #{$btn-floating-width-sm};
  --#{$prefix}btn-height-sm: #{$btn-floating-height-sm};
  --#{$prefix}btn-icon-width-sm: #{$btn-floating-icon-width-sm};
  --#{$prefix}btn-icon-line-height-sm: #{$btn-floating-icon-line-height-sm};

  width: var(--#{$prefix}btn-width);
  height: var(--#{$prefix}btn-height);

  .fas,
  .far,
  .fab {
    width: var(--#{$prefix}btn-icon-width);
    line-height: var(--#{$prefix}btn-icon-line-height);
  }

  &.btn-lg {
    width: var(--#{$prefix}btn-width-lg);
    height: var(--#{$prefix}btn-height-lg);

    .fas,
    .far,
    .fab {
      width: var(--#{$prefix}btn-icon-width-lg);
      line-height: var(--#{$prefix}btn-icon-line-height-lg);
    }
  }

  &.btn-sm {
    width: var(--#{$prefix}btn-width-sm);
    height: var(--#{$prefix}btn-height-sm);

    .fas,
    .far,
    .fab {
      width: var(--#{$prefix}btn-icon-width-sm);
      line-height: var(--#{$prefix}btn-icon-line-height-sm);
    }
  }
}

[class*='btn-outline-'].btn-floating {
  --#{$prefix}btn-icon-width: #{$btn-outline-floating-icon-width};
  --#{$prefix}btn-icon-width-lg: #{$btn-outline-floating-icon-width-lg};
  --#{$prefix}btn-icon-width-sm: #{$btn-outline-floating-icon-width-sm};
  --#{$prefix}btn-icon-line-height: #{$btn-outline-floating-icon-line-height};
  --#{$prefix}btn-icon-line-height-lg: #{$btn-outline-floating-icon-line-height-lg};
  --#{$prefix}btn-icon-line-height-sm: #{$btn-outline-floating-icon-line-height-sm};

  .fas,
  .far,
  .fab {
    width: var(--#{$prefix}btn-icon-width);
    line-height: var(--#{$prefix}btn-icon-line-height);
  }

  &.btn-lg {
    .fas,
    .far,
    .fab {
      width: var(--#{$prefix}btn-icon-width-lg);
      line-height: var(--#{$prefix}btn-icon-line-height-lg);
    }
  }

  &.btn-sm {
    .fas,
    .far,
    .fab {
      width: var(--#{$prefix}btn-icon-width-sm);
      line-height: var(--#{$prefix}btn-icon-line-height-sm);
    }
  }
}

//
// Fixed option
//

.fixed-action-btn {
  --#{$prefix}btn-right: #{$fixed-action-btn-right};
  --#{$prefix}btn-bottom: #{$fixed-action-btn-bottom};
  --#{$prefix}btn-zindex: #{$fixed-action-button-zindex};
  --#{$prefix}btn-padding-top: #{$fixed-action-btn-padding-top};
  --#{$prefix}btn-padding-bottom: #{$fixed-action-btn-padding-bottom};
  --#{$prefix}btn-padding-x: #{$fixed-action-btn-padding-x};
  --#{$prefix}btn-margin-bottom: #{$fixed-action-btn-li-margin-bottom};

  position: fixed;
  right: var(--#{$prefix}btn-right);
  bottom: var(--#{$prefix}btn-bottom);
  z-index: var(--#{$prefix}btn-zindex);
  display: flex;
  flex-flow: column-reverse nowrap;
  align-items: center;
  padding: var(--#{$prefix}btn-padding-top) var(--#{$prefix}btn-padding-x)
    var(--#{$prefix}btn-padding-bottom);
  margin-bottom: 0;
  height: auto;
  overflow: hidden;

  & > .btn-floating {
    position: relative;
    transform: scale(1.2);
    z-index: 10;
  }

  ul {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    margin-bottom: 0;
    text-align: center;
    opacity: 0;
    transition: transform 0.4s, opacity 0.4s;
    z-index: -1;

    li {
      z-index: 0;
      display: flex;
      margin-right: auto;
      margin-bottom: var(--#{$prefix}btn-margin-bottom);
      margin-left: auto;

      &:first-of-type {
        margin-top: calc(var(--#{$prefix}btn-margin-bottom) * 0.5);
      }
    }

    a {
      &.btn {
        opacity: 0;
        transition: opacity 0.4s ease-in;

        &.shown {
          opacity: 1;
        }
      }
    }
  }

  &.active ul {
    opacity: 1;
  }
}

//
// Button block
//

.btn-block {
  --#{$prefix}btn-margin-top: #{$btn-block-margin-top};

  display: block;
  width: 100%;

  // Vertically space out multiple block buttons
  + .btn-block {
    margin-top: var(--#{$prefix}btn-margin-top);
  }
}
