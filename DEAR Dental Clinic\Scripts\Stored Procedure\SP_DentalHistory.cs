using Microsoft.Data.SqlClient;
using DEAR_Dental_Clinic.Scripts.Helper;

namespace DEAR_Dental_Clinic.Scripts.Stored_Procedure
{
	public class SP_DentalHistory
	{
		private readonly DatabaseHelper _dbHelper;

		public SP_DentalHistory()
		{
			_dbHelper = new DatabaseHelper();
		}

		public bool CreateDentalHistoryTable()
		{
			string createTableQuery = @"
				IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = 'Dental_History' AND xtype = 'U')
				BEGIN
					CREATE TABLE Dental_History (
						DentalHistoryID INT PRIMARY KEY IDENTITY(1,1),
						PreviousDentistDr NVARCHAR(MAX) NULL,
						LastDentalVisit NVARCHAR(MAX) NULL,
						PatientID INT
						);
						PRINT 'Dental History table created successfully.';
				END
				ELSE
				BEGIN
					PRINT 'Dental History table already exists.';
				END
			";
			try
			{
				using (SqlConnection connection = _dbHelper.GetConnection())
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(createTableQuery, connection))
					{

						command.ExecuteNonQuery();
						Console.WriteLine("Dental History table creation process completed successfully.");
						return true;
					}

				}
			}
			catch (SqlException ex)
			{
				Console.WriteLine($"SQL Error creating Dental History table: {ex.Message}");
				return false;
			}
			catch (Exception ex)
			{
				Console.WriteLine($"General Error creating Dental History table: {ex.Message}");
				return false;
			}
		}
		public bool DropDentalHistoryTable()
		{
			string dropTableQuery = @"
				IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Dental_History' AND xtype = 'U')
				BEGIN
					DROP TABLE Dental_History;
					PRINT 'Dental History table dropped successfully.';
				END
				ELSE
				BEGIN
					PRINT 'Dental History table does not exist.';
				END
			";
			try
			{
				using (SqlConnection connection = _dbHelper.GetConnection())
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(dropTableQuery, connection))
					{
						command.ExecuteNonQuery();
						Console.WriteLine("Dental History table drop process completed successfully.");
						return true;
					}
				}
			}
			catch (SqlException ex)
			{
				Console.WriteLine($"SQL Error dropping Dental History table: {ex.Message}");
				return false;
			}
			catch (Exception ex)
			{
				Console.WriteLine($"General Error dropping Dental History table: {ex.Message}");
				return false;
			}


		}

		public bool CheckDentalHistoryTableExist()
		{
			string checkTableQuery = @"
				SELECT COUNT (*)
				FROM INFORMATION_SCHEMA.TABLES
				WHERE TABLE_NAME = 'Dental_History' AND TABLE_SCHEMA = 'dbo'
			";
			try
			{
				using (SqlConnection connection = _dbHelper.GetConnection())
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
					{
						int count = (int)command.ExecuteScalar();
						bool exists = count > 0;
						Console.WriteLine($"Dental History table exists: {exists}");
						return exists;
					}
				}
			}
			catch (SqlException ex)
			{
				Console.WriteLine($"SQL Error checking Dental History table: {ex.Message}");
				return false;
			}
			catch (Exception ex)
			{
				Console.WriteLine($"General Error checking Dental History table: {ex.Message}");
				return false;
			}

		}
	}
}