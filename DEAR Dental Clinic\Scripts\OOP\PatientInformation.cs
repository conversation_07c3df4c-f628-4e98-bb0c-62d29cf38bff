namespace Dear_Dental_Clinic.Scripts.OOP
{
	public class PatientInformation
	{
		public int PatientID { get; set; }
		public string? FirstName { get; set; }
		public string? MiddleName { get; set; }
		public string? LastName { get; set; }
		public DateTime? Birthdate { get; set; }
		public int? Age { get; set; } // please include asap
		public string? Gender { get; set; }
		public string? Religion { get; set; }
		public string? Nationality { get; set; }
		public string? Nickname { get; set; }
		public string? HomeAddress { get; set; }
		public string? HomeNo { get; set; }
		public string? Occupation { get; set; }
		public string? OfficeNo { get; set; }
		public string? Dental_Insurance { get; set; }
		public string? FaxNo { get; set; }
		public DateTime? EffectiveDate { get; set; }
		public string? PhoneNo { get; set; }
		public string? Email { get; set; }
		public string? GuardianName { get; set; }
		public string? GuardianOccupation { get; set; }
		public string? Question1 { get; set; }
		public string? Question2 { get; set; }
	}
}