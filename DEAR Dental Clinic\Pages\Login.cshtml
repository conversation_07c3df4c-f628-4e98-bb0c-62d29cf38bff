@page
@model DEAR_Dental_Clinic.Pages.LoginModel
@{
    ViewData["Title"] = "Login - DEAR Dental Clinic";
}

<div class="login-page">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="text-center mb-4">
                        <i class="fas fa-tooth fa-3x text-primary mb-3"></i>
                        <h2>DEAR Dental Clinic</h2>
                        <p>Sign in to your account</p>
                    </div>

                    <form method="post">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" placeholder="Enter your email" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">Password</label>
                            <input asp-for="Input.Password" type="password" class="form-control" placeholder="Enter your password" />
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="Input.RememberMe" class="form-check-input" type="checkbox" />
                                <label asp-for="Input.RememberMe" class="form-check-label">Remember me</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">Sign In</button>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none">Forgot your password?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
