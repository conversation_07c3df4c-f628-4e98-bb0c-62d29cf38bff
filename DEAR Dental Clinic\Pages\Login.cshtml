@page
@model DEAR_Dental_Clinic.Pages.LoginModel
@{
    ViewData["Title"] = "Login - DEAR Dental Clinic";
}

<!-- MDB5 Login Page -->
<div class="login-page bg-gradient-primary">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-4" data-mdb-animation-init data-mdb-animation="fade-in">
                <div class="card shadow-5 rounded-6">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-tooth fa-3x text-primary mb-3"></i>
                            <h2 class="fw-bold text-primary">DEAR Dental Clinic</h2>
                            <p class="text-muted">Sign in to your account</p>
                        </div>

                        <form method="post">
                            <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                            <!-- MDB5 Form Outline -->
                            <div class="form-outline mb-4">
                                <input asp-for="Input.Email" type="email" class="form-control form-control-lg" />
                                <label asp-for="Input.Email" class="form-label">Email Address</label>
                                <span asp-validation-for="Input.Email" class="text-danger"></span>
                            </div>

                            <div class="form-outline mb-4">
                                <input asp-for="Input.Password" type="password" class="form-control form-control-lg" />
                                <label asp-for="Input.Password" class="form-label">Password</label>
                                <span asp-validation-for="Input.Password" class="text-danger"></span>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-100 rounded-pill shadow-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Initialize MDB5 form validation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all form outlines
            const formOutlines = document.querySelectorAll('.form-outline');
            formOutlines.forEach((formOutline) => {
                new mdb.Input(formOutline).init();
            });
        });
    </script>
}
