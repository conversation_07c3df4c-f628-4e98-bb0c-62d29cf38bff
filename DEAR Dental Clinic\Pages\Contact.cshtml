@page
@model DEAR_Dental_Clinic.Pages.ContactModel
@{
    ViewData["Title"] = "Contact - DEAR Dental Clinic";
}

<!-- MDB5 Enhanced Contact Page -->
<div class="container py-5">
    <!-- Header Section -->
    <div class="row">
        <div class="col-12 text-center mb-5" data-mdb-animation-init data-mdb-animation="fade-in-up">
            <h1 class="display-5 fw-bold text-primary mb-3">Contact Us</h1>
            <p class="lead text-muted">Get in touch to schedule your appointment</p>
        </div>
    </div>

    <!-- Contact Cards -->
    <div class="row mb-5">
        <div class="col-lg-4 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="100">
            <div class="card h-100 shadow-3 border-0 text-center hover-shadow">
                <div class="card-body p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-map-marker-alt text-primary fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Address</h5>
                    <p class="text-muted mb-0">123 Dental Street<br>Healthcare City, HC 12345</p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="200">
            <div class="card h-100 shadow-3 border-0 text-center hover-shadow">
                <div class="card-body p-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-phone text-success fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Phone</h5>
                    <p class="text-muted mb-2">(*************</p>
                    <p class="text-danger mb-0"><small><i class="fas fa-exclamation-triangle me-1"></i>Emergency: (*************</small></p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 col-sm-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-up" data-mdb-animation-delay="300">
            <div class="card h-100 shadow-3 border-0 text-center hover-shadow">
                <div class="card-body p-4">
                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-envelope text-info fa-2x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Email</h5>
                    <p class="text-muted mb-1"><EMAIL></p>
                    <p class="text-muted mb-0"><EMAIL></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Form and Office Hours -->
    <div class="row">
        <div class="col-lg-8 col-md-12 mb-4" data-mdb-animation-init data-mdb-animation="fade-in-left">
            <div class="card shadow-4 border-0">
                <div class="card-body p-5">
                    <h3 class="fw-bold text-primary mb-4">
                        <i class="fas fa-paper-plane me-2"></i>Send us a Message
                    </h3>

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show shadow-3" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-mdb-dismiss="alert"></button>
                        </div>
                    }

                    <form method="post">
                        <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="form-outline">
                                    <input asp-for="Input.FirstName" type="text" class="form-control form-control-lg" />
                                    <label asp-for="Input.FirstName" class="form-label">First Name</label>
                                    <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="form-outline">
                                    <input asp-for="Input.LastName" type="text" class="form-control form-control-lg" />
                                    <label asp-for="Input.LastName" class="form-label">Last Name</label>
                                    <span asp-validation-for="Input.LastName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="form-outline">
                                    <input asp-for="Input.Email" type="email" class="form-control form-control-lg" />
                                    <label asp-for="Input.Email" class="form-label">Email Address</label>
                                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="form-outline">
                                    <input asp-for="Input.Phone" type="tel" class="form-control form-control-lg" />
                                    <label asp-for="Input.Phone" class="form-label">Phone Number</label>
                                    <span asp-validation-for="Input.Phone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-outline mb-4">
                            <input asp-for="Input.Subject" type="text" class="form-control form-control-lg" />
                            <label asp-for="Input.Subject" class="form-label">Subject</label>
                            <span asp-validation-for="Input.Subject" class="text-danger"></span>
                        </div>

                        <div class="form-outline mb-4">
                            <textarea asp-for="Input.Message" class="form-control form-control-lg" rows="5"></textarea>
                            <label asp-for="Input.Message" class="form-label">Message</label>
                            <span asp-validation-for="Input.Message" class="text-danger"></span>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg rounded-pill shadow-3">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-12" data-mdb-animation-init data-mdb-animation="fade-in-right">
            <div class="card shadow-4 border-0 h-100">
                <div class="card-body p-4">
                    <h3 class="fw-bold text-primary mb-4">
                        <i class="fas fa-clock me-2"></i>Office Hours
                    </h3>
                    <div class="hours-list">
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Monday</span>
                            <span class="text-success">8:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Tuesday</span>
                            <span class="text-success">8:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Wednesday</span>
                            <span class="text-success">8:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Thursday</span>
                            <span class="text-success">8:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Friday</span>
                            <span class="text-success">8:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2 border-bottom">
                            <span class="fw-bold">Saturday</span>
                            <span class="text-danger">Closed</span>
                        </div>
                        <div class="hours-item d-flex justify-content-between align-items-center py-2">
                            <span class="fw-bold">Sunday</span>
                            <span class="text-danger">Closed</span>
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-danger bg-opacity-10 rounded-3">
                        <h5 class="fw-bold text-danger mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>Emergency Care
                        </h5>
                        <p class="text-muted mb-0">For dental emergencies outside office hours, please call our emergency line at <strong>(*************</strong>.</p>
                    </div>

                    <div class="mt-4 text-center">
                        <a href="/BookAppointment" class="btn btn-primary rounded-pill shadow-3">
                            <i class="fas fa-calendar-plus me-2"></i>Book Appointment
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Initialize MDB5 form validation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all form outlines
            const formOutlines = document.querySelectorAll('.form-outline');
            formOutlines.forEach((formOutline) => {
                new mdb.Input(formOutline).init();
            });
        });
    </script>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
