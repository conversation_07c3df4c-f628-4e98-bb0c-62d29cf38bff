@page
@model DEAR_Dental_Clinic.Pages.ContactModel
@{
    ViewData["Title"] = "Contact - DEAR Dental Clinic";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h1 class="section-title">Contact Us</h1>
            <p class="section-subtitle">Get in touch to schedule your appointment</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h5>Address</h5>
                <p>123 Dental Street<br>Healthcare City, HC 12345</p>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <h5>Phone</h5>
                <p>(*************<br>Emergency: (*************</p>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h5>Email</h5>
                <p><EMAIL><br><EMAIL></p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-12 mb-4">
            <div class="contact-form-card">
                <h3>Send us a Message</h3>
                <form method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.FirstName" class="form-label">First Name</label>
                            <input asp-for="Input.FirstName" class="form-control" placeholder="Your first name" />
                            <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.LastName" class="form-label">Last Name</label>
                            <input asp-for="Input.LastName" class="form-control" placeholder="Your last name" />
                            <span asp-validation-for="Input.LastName" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" placeholder="<EMAIL>" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.Phone" class="form-label">Phone</label>
                            <input asp-for="Input.Phone" class="form-control" placeholder="(*************" />
                            <span asp-validation-for="Input.Phone" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Input.Subject" class="form-label">Subject</label>
                        <input asp-for="Input.Subject" class="form-control" placeholder="What is this regarding?" />
                        <span asp-validation-for="Input.Subject" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Input.Message" class="form-label">Message</label>
                        <textarea asp-for="Input.Message" class="form-control" rows="5" placeholder="Tell us how we can help you..."></textarea>
                        <span asp-validation-for="Input.Message" class="text-danger"></span>
                    </div>

                    <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                </form>
            </div>
        </div>

        <div class="col-lg-4 col-md-12">
            <div class="office-hours-card">
                <h3>Office Hours</h3>
                <div class="hours-list">
                    <div class="hours-item">
                        <span class="day">Monday</span>
                        <span class="time">8:00 AM - 6:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Tuesday</span>
                        <span class="time">8:00 AM - 6:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Wednesday</span>
                        <span class="time">8:00 AM - 6:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Thursday</span>
                        <span class="time">8:00 AM - 6:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Friday</span>
                        <span class="time">8:00 AM - 5:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Saturday</span>
                        <span class="time">9:00 AM - 2:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span class="day">Sunday</span>
                        <span class="time">Closed</span>
                    </div>
                </div>

                <div class="mt-4">
                    <h4>Emergency Care</h4>
                    <p>For dental emergencies outside office hours, please call our emergency line at (*************.</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
