using Microsoft.Data.SqlClient;
using DEAR_Dental_Clinic.Scripts.Helper;

namespace DEAR_Dental_Clinic.Scripts.Stored_Procedure
{
    public class SP_MedicalHistory
    {
        private readonly DatabaseHelper _dbHelper;

        public SP_MedicalHistory()
        {
            _dbHelper = new DatabaseHelper();
        }

        public bool CreateMedicalHistoryTable()
        {
            string createTableQuery = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = 'Medical_History' AND xtype = 'U')
                BEGIN
                    CREATE TABLE Medical_History (
                        MedicalHistoryID INT PRIMARY KEY IDENTITY(1,1),
                        PhysicianName NVARCHAR(MAX) NULL,
                        SpecialtyIfAvailable NVARCHAR(MAX) NULL,
                        OfficeAddress NVARCHAR(MAX) NULL,
                        OfficeNumber NVARCHAR(50) NULL,
                        Q1 BIT NULL,
                        Q2 BIT NULL,
                        Q2sub NVARCHAR(MAX) NULL,
                        Q3 BIT NULL,
                        Q3sub NVARCHAR(MAX) NULL,
                        Q4 BIT NULL,
                        Q4sub NVARCHAR(MAX) NULL,
                        Q5 BIT NULL,
                        Q5sub NVARCHAR(MAX) NULL,
                        Q6 BIT NULL,
                        Q7 BIT NULL,
                        Q8 NVARCHAR(MAX) NULL,
                        Q8sub NVARCHAR(MAX) NULL,
                        Q9 NVARCHAR(MAX) NULL,
                        Q10 NVARCHAR(MAX) NULL,
                        Q11 NVARCHAR(MAX) NULL,
                        Q12 NVARCHAR(MAX) NULL,
                        Q13 NVARCHAR(MAX) NULL,
                        Signature VARBINARY(MAX) NULL,
                        PatientID INT
                    );
                    PRINT 'Medical History table created successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Medical History table already exists.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(createTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Medical History table creation process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error creating Medical History table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error creating Medical History table: {ex.Message}");
                return false;
            }
        }

        public bool DropMedicalHistoryTable()
        {
            string dropTableQuery = @"
                IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Medical_History' AND xtype = 'U')
                BEGIN
                    DROP TABLE Medical_History;
                    PRINT 'Medical History table dropped successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Medical History table does not exist.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(dropTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Medical History table drop process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error dropping Medical History table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error dropping Medical History table: {ex.Message}");
                return false;
            }
        }

        public bool CheckMedicalHistoryTableExists()
        {
            string checkTableQuery = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'Medical_History' AND TABLE_SCHEMA = 'dbo'
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
                    {
                        int count = (int)command.ExecuteScalar();
                        bool exists = count > 0;
                        Console.WriteLine($"Medical History table exists: {exists}");
                        return exists;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error checking Medical History table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error checking Medical History table: {ex.Message}");
                return false;
            }
        }
    }
}
