using Microsoft.Data.SqlClient;
using DEAR_Dental_Clinic.Scripts.Helper;

namespace DEAR_Dental_Clinic.Scripts.Stored_Procedure
{
    public class SP_PatientInformation
    {
        private readonly DatabaseHelper _dbHelper;

        public SP_PatientInformation()
        {
            _dbHelper = new DatabaseHelper();
        }

        public bool CreatePatientInformationTable()
        {
            string createTableQuery = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = 'Patient_Information' AND xtype = 'U')
                BEGIN
                    CREATE TABLE Patient_Information (
                        PatientID INT PRIMARY KEY IDENTITY(1,1),
                        FirstName NVARCHAR(MAX) NULL,
                        MiddleName NVARCHAR(MAX) NULL,
                        LastName NVARCHAR(MAX) NULL,
                        Birthdate DATETIME2 NULL,
                        Age INT NULL,
                        Gender NVARCHAR(50) NULL,
                        Religion NVARCHAR(MAX) NULL,
                        Nationality NVARCHAR(MAX) NULL,
                        Nickname NVARCHAR(MAX) NULL,
                        HomeAddress NVARCHAR(MAX) NULL,
                        HomeNo NVARCHAR(50) NULL,
                        Occupation NVARCHAR(MAX) NULL,
                        OfficeNo NVARCHAR(50) NULL,
                        Dental_Insurance NVARCHAR(MAX) NULL,
                        FaxNo NVARCHAR(50) NULL,
                        EffectiveDate DATETIME2 NULL,
                        PhoneNo NVARCHAR(50) NULL,
                        Email NVARCHAR(255) NULL,
                        GuardianName NVARCHAR(MAX) NULL,
                        GuardianOccupation NVARCHAR(MAX) NULL,
                        Question1 NVARCHAR(MAX) NULL,
                        Question2 NVARCHAR(MAX) NULL
                    );
                    PRINT 'Patient Information table created successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Patient Information table already exists.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(createTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Patient Information table creation process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error creating Patient Information table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error creating Patient Information table: {ex.Message}");
                return false;
            }
        }

        public bool DropPatientInformationTable()
        {
            string dropTableQuery = @"
                IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Patient_Information' AND xtype = 'U')
                BEGIN
                    DROP TABLE Patient_Information;
                    PRINT 'Patient Information table dropped successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Patient Information table does not exist.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(dropTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Patient Information table drop process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error dropping Patient Information table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error dropping Patient Information table: {ex.Message}");
                return false;
            }
        }

        public bool CheckPatientInformationTableExists()
        {
            string checkTableQuery = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'Patient_Information' AND TABLE_SCHEMA = 'dbo'
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
                    {
                        int count = (int)command.ExecuteScalar();
                        bool exists = count > 0;
                        Console.WriteLine($"Patient Information table exists: {exists}");
                        return exists;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error checking Patient Information table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error checking Patient Information table: {ex.Message}");
                return false;
            }
        }
    }
}
