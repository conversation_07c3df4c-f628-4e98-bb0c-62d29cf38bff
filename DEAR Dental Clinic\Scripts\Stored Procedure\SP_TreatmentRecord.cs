using Microsoft.Data.SqlClient;
using DEAR_Dental_Clinic.Scripts.Helper;

namespace DEAR_Dental_Clinic.Scripts.Stored_Procedure
{
    public class SP_TreatmentRecord
    {
        private readonly DatabaseHelper _dbHelper;

        public SP_TreatmentRecord()
        {
            _dbHelper = new DatabaseHelper();
        }

        public bool CreateTreatmentRecordTable()
        {
            string createTableQuery = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name = 'Treatment_Record' AND xtype = 'U')
                BEGIN
                    CREATE TABLE Treatment_Record (
                        TreatmentID INT PRIMARY KEY IDENTITY(1,1),
                        Name NVARCHAR(MAX) NULL,
                        Age INT NULL,
                        Gender NVARCHAR(50) NULL,
                        TreatmentDate DATETIME2 NULL,
                        ToothNo INT NULL,
                        TreatmentProcedure NVARCHAR(MAX) NULL,
                        Dentists NVARCHAR(MAX) NULL,
                        AmountCharge DECIMAL(18,2) NULL,
                        AmountPaid DECIMAL(18,2) NULL,
                        AmountBalance DECIMAL(18,2)
                    );
                    PRINT 'Treatment Record table created successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Treatment Record table already exists.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(createTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Treatment Record table creation process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error creating Treatment Record table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error creating Treatment Record table: {ex.Message}");
                return false;
            }
        }

        public bool DropTreatmentRecordTable()
        {
            string dropTableQuery = @"
                IF EXISTS (SELECT * FROM sysobjects WHERE name = 'Treatment_Record' AND xtype = 'U')
                BEGIN
                    DROP TABLE Treatment_Record;
                    PRINT 'Treatment Record table dropped successfully.';
                END
                ELSE
                BEGIN
                    PRINT 'Treatment Record table does not exist.';
                END
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(dropTableQuery, connection))
                    {
                        command.ExecuteNonQuery();
                        Console.WriteLine("Treatment Record table drop process completed successfully.");
                        return true;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error dropping Treatment Record table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error dropping Treatment Record table: {ex.Message}");
                return false;
            }
        }

        public bool CheckTreatmentRecordTableExists()
        {
            string checkTableQuery = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = 'Treatment_Record' AND TABLE_SCHEMA = 'dbo'
            ";
            try
            {
                using (SqlConnection connection = _dbHelper.GetConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
                    {
                        int count = (int)command.ExecuteScalar();
                        bool exists = count > 0;
                        Console.WriteLine($"Treatment Record table exists: {exists}");
                        return exists;
                    }
                }
            }
            catch (SqlException ex)
            {
                Console.WriteLine($"SQL Error checking Treatment Record table: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error checking Treatment Record table: {ex.Message}");
                return false;
            }
        }
    }
}
